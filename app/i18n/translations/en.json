{"common": {"income": "Income", "expense": "Expense", "today": "Today", "yesterday": "Yesterday", "save": "Save", "cancel": "Cancel", "delete": "Delete", "ok": "OK", "error": "Error", "edit": "Edit", "add": "Add", "category": "Category", "amount": "Amount", "note": "Note", "date": "Date", "member": "Member", "refunded": "Refunded", "refund": "Refund", "allMembers": "All Members", "monthlyBudget": "Monthly Budget", "transactionRecord": "Transactions", "all": "All", "noTransactions": "No transactions yet", "clickAddButtonToRecord": "Click the plus button in the bottom right corner to record", "monthlyIncome": "Monthly Income", "monthlyExpense": "Monthly Expense", "yearly": "Yearly", "custom": "Custom", "monthly": "Monthly", "tag": "Tag", "noData": "No data", "confirmDeleteMember": "Confirm Delete Member", "confirmDeleteTag": "Confirm Delete Tag", "confirmDeleteTransaction": "Confirm Delete Transaction", "confirmDeleteTransactionMessage": "Are you sure you want to delete this transaction? This action cannot be undone.", "weekdays": {"mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun"}, "close": "Close", "to": "to", "loading": "Loading", "loadMore": "Load more", "noMoreData": "No more data", "search": "Search note/category/member", "used": "Used", "remaining": "Remaining", "members": "Members", "budget": "Monthly Budget", "noBudget": "No Monthly Budget", "confirm": "Confirm", "thanks": "Thanks", "confirmDelete": "Confirm Delete", "deleteMemberFailed": "Delete member failed", "memberName": "Member name", "budgetAmount": "Monthly Budget Amount", "addMember": "Add member", "addMemberFailed": "Add member failed", "updateMemberFailed": "Update member failed", "defaultMemberCannotBeDeleted": "Default member cannot be deleted", "noMembers": "No members", "addMembersToRecord": "Add family members to record", "alert": "<PERSON><PERSON>", "pleaseInputTagName": "Please input tag name", "addTagFailed": "Add tag failed", "updateTagFailed": "Update tag failed", "deleteTagFailed": "Delete tag failed", "noTags": "No tags", "clickAddButtonToAddTag": "Click the plus button in the bottom right corner to add tag", "addNewTag": "Add New Tag", "tagName": "Tag name", "emailUnavailable": "Email function is not available", "noMember": "No Member", "updateBudgetFailed": "Update budget failed", "noBudgetPrompt": "Set up your budget in the profile page to track your spending\nor switch to a member with a set budget\n or to hide this module in the settings", "confirmDeleteTagPrompt": "Are you sure you want to delete this tag?", "excludeFromBudget": "Exclude from monthly budget"}, "home": {"title": "Home", "stats": "Stats", "profile": "Profile", "noTransactions": "No transactions yet"}, "add": {"title": "Create", "editTitle": "Edit", "addIncome": "Add Income", "addExpense": "Add Expense", "favorites": "Favorites", "new": "New", "addToFavorites": "Add to Favorites", "noFavorites": "No favorites yet", "clickAddButtonToCreate": "Click the plus button in the bottom right corner to create", "saveFailed": "Save failed", "validation": {"amountRequired": "Please enter a valid amount greater than 0", "categoryRequired": "Please select a category"}}, "categories": {"title": "Categories", "addCategory": "Add Category", "categoryName": "Category name", "selectIcon": "Select Icon", "noCategories": "No categories yet", "income": "Income Categories", "expense": "Expense Categories", "categoryNameRequired": "Please input category name", "addCategoryFailed": "Add category failed", "updateCategoryFailed": "Update category failed", "deleteCategoryFailed": "Delete category failed", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this category?", "delete": "Delete", "cancel": "Cancel", "editCategory": "Edit Category"}, "profile": {"profile": "Profile", "memberBudget": "Member Management And Monthly Budget", "exportExcel": "Export Excel", "manageCategories": "Manage Categories", "manageTags": "Manage Tags", "settings": "Settings", "budget": {"title": "Budget Settings", "monthly": "Monthly Budget", "daily": "Daily Budget"}, "premium": {"title": "Premium Feature", "description": "Unlock tag management and more premium features", "feature1": "Unlimited tags for better expense tracking", "feature2": "More detailed statistics and insights", "purchase": "Upgrade to Premium", "badge": "Premium", "success": "Purchase Successful", "enjoyFeatures": "Thank you for your purchase! You can now enjoy all premium features.", "failed": "Purchase Failed", "tryAgain": "Please try again later.", "notAvailable": "Product Not Available", "error": "Error", "requiredTitle": "Premium Feature", "requiredDesc": "Tags feature requires premium upgrade. Would you like to upgrade now?", "upgrade": "Upgrade", "alreadyPremium": "Already Premium", "alreadyPremiumDesc": "You already have premium access to all features.", "restore": "Restore Purchase"}, "export": {"title": "Export Transactions", "emailPlaceholder": "Enter your email address", "invalidEmail": "Please enter a valid email address", "success": "Export Successful", "sent": "The export file has been sent to your email", "failed": "Export failed, please try again later", "emailUnavailable": "Email function is not available", "exportToEmail": "Export to Email", "exportToLocal": "Save to Device", "saveFile": "Save File", "sharingUnavailable": "Sharing is not available on this device", "localExportSuccess": "File saved successfully"}, "rateApp": "Rate App", "rateAppMessage": "If you like this app, please rate us in the app store!", "rateAppThanks": "Thank you for your support!", "importExcel": "Import Excel", "import": {"title": "Import Transactions", "selectFile": "Select Excel File", "processing": "Processing...", "success": "Import Successful", "successMessage": "Successfully imported {count} transactions", "failed": "Import Failed", "invalidFormat": "Invalid file format. Please use Excel (.xlsx) file", "fileNotFound": "File not found", "instructions": "If the file is exported by this app, it can be imported directly. If the file is exported by other apps, it should contain the following headers: date, type (income/expense), category, amount, note.", "instructionsPremium": "Tags feature is only available for premium users. Non-premium users will have tags column ignored during import."}, "appName": "NineCents"}, "settings": {"title": "Settings", "general": "General Settings", "display": "Display Settings", "currency": "<PERSON><PERSON><PERSON><PERSON>", "hideBudgetModule": "Hide Home Budget Module", "hideMemberSection": "Hide Member <PERSON><PERSON>", "hideExcludeFromBudget": "Hide Exclude from Budget Option", "theme": "Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode (Same as Light)", "appearance": "Appearance", "success": "Success", "error": "Error", "currencyUpdated": "Currency has been updated", "updateFailed": "Update failed, please try again"}, "budget": {"title": "Budget", "monthly": "Monthly Budget", "daily": "Daily Budget", "totalBudget": "Total Monthly Budget"}, "stats": {"detail": "StatsDetail"}, "onboarding": {"welcome": "Welcome to NineCents", "welcomeMessage": "Let's quickly set up and start your accounting journey!", "step1Title": "Tap here to start recording", "step1Description": "Tap the plus button in the bottom right corner to add your first transaction", "step2Title": "Select a category", "step2Description": "Tap here to expand category selection and choose an appropriate category", "addCategory": "Add Category", "addTransaction": "Add Transaction", "skip": "Skip Guide", "next": "Next", "finish": "Finish Guide", "getStarted": "Get Started", "skipConfirm": "Are you sure you want to skip the onboarding guide?", "skipConfirmMessage": "You can manage categories and record transactions later in the profile page.", "gotIt": "Got it"}}