{"name": "ninecents", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "start-original": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "build:ios": "eas build --platform ios --profile production", "build:ios-dev": "eas build --platform ios --profile development", "pods:install": "cd ios && pod install --repo-update", "pods:clean": "cd ios && rm -rf Pods Podfile.lock && pod install --repo-update", "clean": "rm -rf node_modules && npm install && npm run pods:clean"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "dayjs": "^1.11.13", "expo": "^53.0.16", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-constants": "~17.1.6", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-in-app-purchases": "~14.5.0", "expo-linking": "~7.1.6", "expo-localization": "~16.1.6", "expo-mail-composer": "~14.1.5", "expo-modules-core": "~2.4.2", "expo-router": "~5.1.2", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.9", "expo-sqlite": "~15.2.13", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "i18n-js": "^4.5.1", "jest": "^29.2.1", "jest-expo": "~53.0.8", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-draggable-flatlist": "^4.0.1", "react-native-gesture-handler": "~2.24.0", "react-native-localize": "^3.4.1", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "babel-plugin-transform-remove-console": "^6.9.4", "react-test-renderer": "18.3.1", "typescript": "~5.8.3", "web-streams-polyfill": "^4.1.0"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0", "@types/react": "~19.0.10", "@types/react-dom": "~19.0.10"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-chart-kit", "expo-in-app-purchases", "xlsx"]}}}, "private": true}